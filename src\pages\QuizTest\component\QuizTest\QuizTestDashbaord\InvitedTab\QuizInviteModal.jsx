import { useSelector } from "react-redux";
import { <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, ModalHeader } from "reactstrap";
import QuizInviteForm from "./QuizInviteForm";
import { RxCross1 } from "react-icons/rx";
import CustomLoader from "../../../../../../components/sharedComponents/CustomLoader";

const QuizInviteModal = ({ toggle, isopen, type, formData }) => {
  const closeBtn = (
    <button className="close" onClick={toggle}>
      <RxCross1 />
    </button>
  );

  const {
    quizTestsList,
    inviteFormLoading,
    editInviteLoading,
    quizTestsListLoading,
  } = useSelector((state) => state.quizTest || {});
  return (
    <>
      <Modal isOpen={isopen} toggle={toggle}>
        <ModalHeader toggle={toggle} close={closeBtn}>
          Invite Candidate
        </ModalHeader>
        <ModalBody>
          <QuizInviteForm toggle={toggle} type={type} formData={formData} />
        </ModalBody>
      </Modal>
    </>
  );
};

export default QuizInviteModal;
