import { useEffect } from "react";
import { Field, reduxForm } from "redux-form";
import { useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";

import { required, email } from "../../../../../../components/utils/validators";
import {
  renderInputField,
  renderSelectField,
} from "../../../../../../components/sharedComponents/ReduxFormFields";
import {
  inviteCandidate,
  getQuizTestsList,
  getQuizTestInvitesList,
  editInviteCandidate,
} from "../../../../actions/operations";
import CustomLoader from "../../../../../../components/sharedComponents/CustomLoader";

const QuizInviteForm = ({
  toggle,
  handleSubmit,
  type,
  formData,
  initialize,
  submitting,
}) => {
  const dispatch = useDispatch();
  const { id } = useParams();

  const {
    quizTestsList,
    inviteFormLoading,
    editInviteLoading,
    quizTestsListLoading,
  } = useSelector((state) => state.quizTest || {});

  useEffect(() => {
    dispatch(getQuizTestsList({}));
  }, []);

  useEffect(() => {
    if (quizTestsList && quizTestsList.length > 0) {
      const selectedQuiz = quizTestsList.find((quiz) => quiz._id === id);

      const initialData = () => {
        if (type === "edit" && formData) {
          return { ...formData, quizId: selectedQuiz?._id };
        } else {
          return { quizId: selectedQuiz?._id };
        }
      };

      initialize(initialData());
    }
  }, [initialize]);

  const quizOptions = Array.isArray(quizTestsList)
    ? quizTestsList.map((quiz) => ({
        value: quiz._id,
        label: quiz.quizTitle,
      }))
    : [];

  const onSubmit = (values) => {
    const formdata = { ...values };

    if (id && !formdata.quizId) {
      formdata.quizId = id;
    }

    let quizInvite = formdata;

    if (type === "edit") {
      dispatch(editInviteCandidate(quizInvite))
        .then((res) => {
          if (res && res.success) {
            dispatch(getQuizTestInvitesList({}));
            toggle();
          }
        })
        .catch((error) => {
          console.error("Error updating candidate:", error);
        });
    } else {
      dispatch(inviteCandidate(quizInvite))
        .then((res) => {
          if (res && res.success) {
            dispatch(getQuizTestInvitesList({}));
            toggle();
          }
        })
        .catch((error) => {
          console.error("Error inviting candidate:", error);
        });
    }
  };

  return (
    <>
      <div className="row mx-0">
        {inviteFormLoading || editInviteLoading || quizTestsListLoading ? (
          <div className="d-flex justify-content-center align-items-center w-100">
            <CustomLoader />
          </div>
        ) : (
          <div className="col-12">
            <form className="w-100" onSubmit={handleSubmit(onSubmit)}>
              <Field
                name="quizId"
                className="form-control mb-3"
                placeholder="Select Quiz"
                component={renderSelectField}
                validate={[required]}
                textField="label"
                options={quizOptions}
                label="Select Quiz"
              />
              <Field
                name="email"
                type="text"
                className="form-control mb-3"
                placeholder="Enter Email"
                component={renderInputField}
                isRequired={true}
                validate={[required, email]}
                label="Email"
              />
              <Field
                name="expDate"
                type="date"
                className="form-control mb-3"
                placeholder="Enter Expiry Date"
                component={renderInputField}
                validate={[required]}
                isRequired={true}
                label="Expiry Date"
              />
              <div className="d-flex justify-content-end mt-3">
                <button
                  type="submit"
                  className="btn btn-primary mr-3"
                  disabled={submitting}
                >
                  <span>{type === "edit" ? "Edit Invite" : "Invite Quiz"}</span>
                </button>
                <button
                  className="btn btn-secondary"
                  onClick={(e) => {
                    toggle();
                  }}
                >
                  <span>Close</span>
                </button>
              </div>
            </form>
          </div>
        )}
      </div>
    </>
  );
};

export default reduxForm({
  form: "quizInviteForm",
})(QuizInviteForm);
