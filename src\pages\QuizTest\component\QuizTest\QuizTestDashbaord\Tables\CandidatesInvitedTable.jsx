import { useSelector, useDispatch } from "react-redux";
import { useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import CustomLoader from "../../../../../../components/sharedComponents/CustomLoader";
import BootstrapTable from "react-bootstrap-table-next";
import "bootstrap/dist/css/bootstrap.min.css";

import {
  deleteInvite,
  getQuizTestInvitesList,
} from "../../../../actions/operations";

import { MdDelete } from "react-icons/md";
import { FaPencilAlt } from "react-icons/fa";
import QuizInviteModal from "../InvitedTab/QuizInviteModal";
import { Modal, ModalHeader } from "reactstrap";

const CandidatesInvitedTable = ({ quizTestInvitesList }) => {
  const dispatch = useDispatch();
  const { id } = useParams();
  const [isOpen, setIsopen] = useState(false);

  const [formData, setFormData] = useState({
    email: "",
    expDate: "",
  });

  const toggle = () => {
    setIsopen(!isOpen);
  };

  const handleDeleteInvite = (inviteId) => {
    dispatch(deleteInvite(inviteId)).then((res) => {
      dispatch(getQuizTestInvitesList(id));
    });
  };

  const customActionFormatter = (cell, row) => {
    const handleEdit = () => {
      setFormData({
        id: row._id,
        email: row.email,
        expDate: row.expDate,
      });
      toggle();
    };
    return (
      <>
        <MdDelete
          onClick={() => {
            handleDeleteInvite(row._id);
          }}
          className="action-btn mr-2"
          size={25}
        />
        <FaPencilAlt className="action-btn" size={18} onClick={handleEdit} />
      </>
    );
  };

  const candidate_info = [
    {
      id: "1",
      candidate_email: "<EMAIL>",
      candidate_name: "Nitish Gandhi",
      status: "Review Pending",
      expiry_date: "",
      invited_on: "On Mar 2023,4:00 PM IST (Asia/Kolkata)",
      invited_by: "<EMAIL>",
    },
    {
      id: "2",
      candidate_email: "<EMAIL>",
      candidate_name: "Nitish Gandhi",
      status: "Review Pending",
      expiry_date: "",
      invited_on: "On Mar 2023,4:00 PM IST (Asia/Kolkata)",
      invited_by: "<EMAIL>",
    },
  ];

  const headerStyle = {
    width: "20%",
    fontWeight: "500",
    margin: "10px",
    padding: "10px",
    fontSize: "16px",
  };

  const columns = [
    {
      dataField: "_id",
      text: "#",
      headerStyle: { color: "grey", fontWeight: "500", textAlign: "center" },
    },
    {
      dataField: "email",
      text: "Candidate Email",
      headerStyle: headerStyle,
    },
    {
      dataField: "user.firstName",
      text: "Candidate Name",
      headerStyle: headerStyle,
    },
    {
      dataField: "status",
      text: "Status",
      headerStyle: headerStyle,
    },
    {
      dataField: "expDate",
      text: "Expiry Date",
      headerStyle: headerStyle,
    },
    {
      dataField: "createdAt",
      text: "Invited On",
      headerStyle: headerStyle,
    },
    {
      dataField: "invited_by",
      text: "Invited By",
      headerStyle: headerStyle,
    },
    {
      dataField: "actions",
      text: "Actions",
      headerStyle: headerStyle,
      formatter: customActionFormatter,
    },
  ];

  const selectRow = {
    mode: "checkbox",
    clickToSelect: true,
  };

  const { quizTestInvitesListLoading, deleteInviteLoading } = useSelector(
    (state) => state.quizTest
  );

  return (
    <>
      <div className="invited-table table-responsive-sm">
        {quizTestInvitesListLoading || deleteInviteLoading ? (
          <CustomLoader />
        ) : quizTestInvitesList && quizTestInvitesList.length > 0 ? (
          <BootstrapTable
            keyField="id"
            data={quizTestInvitesList}
            columns={columns}
            bordered={false}
            selectRow={selectRow}
          />
        ) : (
          <p>No Data Found</p>
        )}
      </div>
      <QuizInviteModal
        toggle={toggle}
        isopen={isOpen}
        type={"edit"}
        formData={formData}
      />
    </>
  );
};

export default CandidatesInvitedTable;
