import axios from "axios";
import * as actions from "./actionCreators";
import * as constants from "../constants";
import { generateQueryParams } from "../../../components/utils";
import { triggerNotifier } from "../../../components/utils/notifier";

const baseURL = process.env.REACT_APP_BASE_URL;

//quiz test apis

export const createQuizTest = (quizTest) => (dispatch) => {
  dispatch({ type: constants.CREATE_TEST_LOADING });
  return axios
    .post(`${baseURL}/quiz/test/create`, quizTest)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: constants.CREATE_TEST_LOADING });
        return { success: true, data: res?.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: constants.CREATE_TEST_LOADING });
      console.log("Create Test Error", error);
      triggerNotifier({
        message: "Mentor availabilties Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getQuizTestsList = (params) => (dispatch) => {
  dispatch({ type: constants.GET_QUIZ_TEST_LIST_LOADING });
  return axios
    .get(
      `${baseURL}/quiz/test${generateQueryParams({
        categories: params.category ? params.category : "",
        access: params.access ? params.access : "",
        status: params.status ? params.status : "",
      })}`
    )
    .then(({ data }) => {
      if (data?.success) {
        dispatch({ type: constants.GET_QUIZ_TEST_LIST_LOADING });
        dispatch(actions.setQuizTestsList(data));
        return { success: true, data: data?.quizTests };
      }
    })
    .catch((error) => {
      dispatch({ type: constants.GET_QUIZ_TEST_LIST_LOADING });
      console.log("get quiz tests error", error);
      triggerNotifier({
        message: "Mentor availabilties Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const deleteQuizTest = (quizTest) => (dispatch) => {
  dispatch({ type: constants.DELETE_LOADING });
  return axios
    .delete(`${baseURL}/quiz/test/${quizTest}`)
    .then(({ data }) => {
      if (data.success) {
        dispatch({ type: constants.DELETE_LOADING });
        dispatch(actions.setQuizTestsList(data));
        return { success: true };
      }
    })
    .catch((error) => {
      console.log("Error in deleting test", error);
      triggerNotifier({
        message: "Mentor availabilties Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const EditQuizTest = (data) => (dispatch) => {
  dispatch({ type: constants.GET_TEST_DATA_LOADING });
  return axios
    .patch(`${baseURL}/quiz/test/${data?.quizTest}`, { data: data })
    .then(({ data }) => {
      if (data) {
        dispatch({ type: constants.GET_TEST_DATA_LOADING });
        dispatch(actions.setQuizTestDetails(data));
        return { success: true };
      }
    })
    .catch((error) => {
      console.log("Error in Edit", error);
      triggerNotifier({
        message: "Mentor availabilties Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const ResetQuizTest = (data) => (dispatch) => {
  dispatch({ type: constants.GET_TEST_DATA_LOADING });
  return axios
    .patch(`${baseURL}/quiz/test/${data.quizId}/reset`, { data: data })
    .then((res) => {
      if (res.status === 200) {
        console.log("response", res);
        // dispatch({ type: constants.GET_TEST_DATA_LOADING });
        // dispatch(actions.setQuizTestDetails(data));
        return { success: true, data: res?.data };
      }
    })
    .catch((error) => {
      console.log("Error in reset", error);
      triggerNotifier({
        message: "Mentor availabilties Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const ResetAllQuizTest = (data) => (dispatch) => {
  dispatch({ type: constants.GET_TEST_DATA_LOADING });
  return axios
    .patch(
      `${baseURL}/quiz/test/${data.quizId}/reset/${generateQueryParams({
        reset: true,
      })}`,
      { data: data }
    )
    .then((res) => {
      if (res.status === 200) {
        console.log("response", res);
        // dispatch({ type: constants.GET_TEST_DATA_LOADING });
        // dispatch(actions.setQuizTestDetails(data));
        return { success: true, data: res?.data };
      }
    })
    .catch((error) => {
      console.log("Error in reset", error);
      triggerNotifier({
        message: "Mentor availabilties Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

//test details api

export const getQuizTestDetails =
  ({ quizTest, mode }) =>
  (dispatch) => {
    dispatch({ type: constants.GET_TEST_DATA_LOADING });
    return axios
      .get(`${baseURL}/quiz/test/${quizTest}${generateQueryParams({ mode })}`)
      .then(({ data }) => {
        if (data.success) {
          dispatch({ type: constants.GET_TEST_DATA_LOADING });
          dispatch(actions.setQuizTestDetails(data));
          return { success: true, data: data };
        }
      })
      .catch((error) => {
        console.log("get quiz tests error", error);
        triggerNotifier({
          message: "Mentor availabilties Error",
          type: "error",
          duration: 1000,
          icon: "⚠️",
        });
      });
  };

export const getTestTakens = (quizTest) => (dispatch) => {
  dispatch({ type: constants.GET_TEST_TAKEN_LOADING });
  return axios
    .get(
      `${baseURL}/quiz/reports/${generateQueryParams({ quizTest: quizTest })}`
    )
    .then(({ data }) => {
      if (data.success) {
        dispatch({ type: constants.GET_TEST_TAKEN_LOADING });
        dispatch(actions.setQuizOverviewTestTaken(data));
        return { success: true, data: data };
      }
    })
    .catch((error) => {
      console.log("get quiz tests taken error", error);
      triggerNotifier({
        message: "Mentor availabilties Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

//Candidate report review api
export const getCandidateReviewReport = (id) => (dispatch) => {
  dispatch({ type: constants.GET_CANDIDATE_DETAILS_LOADING });
  return axios
    .get(`${baseURL}/quiz/test/report/${id}`)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: constants.GET_CANDIDATE_DETAILS_LOADING });
        dispatch(actions.setReportReviewDetails(res?.data));
        return { success: true, data: res?.data };
      }
    })
    .catch((error) => {
      console.log("get quiz tests taken error", error);
      triggerNotifier({
        message: "Mentor availabilties Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

//quiz test question apis

export const createQuizQuestion = (question) => (dispatch) => {
  dispatch({ type: constants.CREATE_QUESTION_LOADING });
  return axios
    .post(`${baseURL}/dayscode/quiz/create`, question)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: constants.CREATE_QUESTION_LOADING });
        return { success: true };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("Add Question Error", error);
      triggerNotifier({
        message: "Mentor availabilties Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getQuizQuestionsList = (quizTest) => (dispatch) => {
  dispatch({ type: constants.GET_TEST_DATA_LOADING });
  return axios
    .get(
      `${baseURL}/dayscode/quiz${generateQueryParams({
        quizTest: quizTest,
      })}`
    )
    .then(({ data }) => {
      if (data.success) {
        dispatch({ type: constants.GET_TEST_DATA_LOADING });
        dispatch(actions.setTestQuizQuestions(data));
        return { success: true, data: data };
      }
    })
    .catch((error) => {
      console.log("getting quiz questions error", error);
      triggerNotifier({
        message: "Mentor availabilties Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const getQuizQuestionDetails = (id) => (dispatch) => {
  dispatch({ type: constants.CREATE_QUESTION_LOADING });
  return axios
    .get(`${baseURL}/dayscode/quiz/${id}`)
    .then(({ data }) => {
      if (data.success) {
        dispatch({ type: constants.CREATE_QUESTION_LOADING });
        dispatch(actions.setQuestionDetail(data));
        return { success: true, data: data };
      }
    })
    .catch((error) => {
      console.log("get quiz test details error", error);
      triggerNotifier({
        message: "Mentor availabilties Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const deleteQuizQuestion = (id) => (dispatch) => {
  dispatch({ type: constants.DELETE_LOADING });
  return axios
    .delete(
      `${baseURL}/dayscode/quiz/${id}${generateQueryParams({
        quizTest: id,
      })}`
    )
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: constants.DELETE_LOADING });
        return { success: true };
      }
    })
    .catch((error) => {
      console.log("Error in deleting question", error);
      triggerNotifier({
        message: "Mentor availabilties Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const editQuizQuestion = (formdata) => (dispatch) => {
  dispatch({ type: constants.CREATE_QUESTION_LOADING });
  return axios
    .patch(`${baseURL}/dayscode/quiz/${formdata._id}`, { data: formdata })
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: constants.CREATE_QUESTION_LOADING });
        dispatch(actions.setQuestionDetail(res?.data));
        return { success: true, data: res?.data };
      }
    })
    .catch((error) => {
      console.log("Error in editing question", error);
      triggerNotifier({
        message: "Mentor availabilties Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const submitQuizTest = (quizTest) => (dispatch) => {
  dispatch({ type: constants.SUBMITTING_QUIZ_TEST_LOADING });
  return axios
    .post(`${baseURL}/quiz/test/${quizTest.quizTestId}/submit`, quizTest)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: constants.SUBMITTING_QUIZ_TEST_LOADING });
        return { success: true, data: res?.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      console.log("Create Test Error", error);
      triggerNotifier({
        message: "Mentor availabilties Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

// ------ Quiz Test Invites ---------

export const getQuizTestInvitesList = (params) => (dispatch) => {
  dispatch({ type: constants.GET_QUIZ_TEST_INVITES_LIST_LOADING });
  return axios
    .get(
      `${baseURL}/quiz/invite${generateQueryParams({
        quizId: params.quizId ? params.quizId : "",
        email: params.email ? params.email : "",
        expDate: params.expDate ? params.expDate : "",
      })}`
    )
    .then(({ data }) => {
      if (data?.success) {
        dispatch(
          actions.setQuizTestInvitesList({ quizTestInvites: data?.invites })
        );
        dispatch({ type: constants.GET_QUIZ_TEST_INVITES_LIST_LOADING });
        return { success: true, data: data?.invites };
      }
    })
    .catch((error) => {
      dispatch({ type: constants.GET_QUIZ_TEST_INVITES_LIST_LOADING });
      console.log("get quiz invites error", error);
      triggerNotifier({
        message: "Get Quiz Invites Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const inviteCandidate = (inviteCandidate) => (dispatch) => {
  dispatch({ type: constants.SUBMITTING_INVITE_FORM_LOADING });
  return axios
    .post(`${baseURL}/quiz/invite/create`, inviteCandidate)
    .then((res) => {
      if (res.status === 201) {
        dispatch({ type: constants.SUBMITTING_INVITE_FORM_LOADING });
        return { success: true, data: res?.data };
      } else {
        return { success: false };
      }
    })
    .catch((error) => {
      dispatch({ type: constants.SUBMITTING_INVITE_FORM_LOADING });
      console.log("Create Test Error", error);
      triggerNotifier({
        message: "Mentor availabilties Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const deleteInvite = (id) => (dispatch) => {
  dispatch({ type: constants.DELETE_INVITE_LOADING });
  return axios
    .delete(`${baseURL}/quiz/invite/${id}`)
    .then((res) => {
      if (res.status === 200) {
        dispatch({ type: constants.DELETE_INVITE_LOADING });
        return { success: true };
      }
    })
    .catch((error) => {
      dispatch({ type: constants.DELETE_INVITE_LOADING });
      console.log("Error in deleting question", error);
      triggerNotifier({
        message: "Mentor availabilties Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};

export const editInviteCandidate = (data) => (dispatch) => {
  dispatch({ type: constants.EDIT_INVITE_LOADING });
  return axios
    .patch(`${baseURL}/quiz/invite/${data.id}`, { data: data })
    .then(({ data }) => {
      if (data) {
        dispatch({ type: constants.EDIT_INVITE_LOADING });
        dispatch(actions.setQuizTestInvitesList(data));
        return { success: true };
      }
    })
    .catch((error) => {
      dispatch({ type: constants.EDIT_INVITE_LOADING });
      console.log("Error in Edit", error);
      triggerNotifier({
        message: "Mentor availabilties Error",
        type: "error",
        duration: 1000,
        icon: "⚠️",
      });
    });
};
